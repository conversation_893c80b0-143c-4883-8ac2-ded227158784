import {
  Markdown,
  CustomTokenizer,
  CustomMarkdownRenderer,
} from '@jgl/biz-components';
import { AiEndTip } from '@jgl/biz-components/src/AiEndTip';
import type { PhotoCutRectDTO } from '@jgl/biz-func';
import { splitContentByTitle } from '@jgl/biz-utils';
import Icon from '@jgl/icon';
import { AcIcon } from '@jgl/icon/src';
import {
  JglText,
  JglTouchable,
  JglView,
  JglXStack,
  JglYStack,
} from '@jgl/ui-v4';
import { isEmpty } from 'lodash';
import { useCallback, useMemo } from 'react';
import { ActivityIndicator, StyleSheet } from 'react-native';
// eslint-disable-next-line import/no-named-as-default
import LinearGradient from 'react-native-linear-gradient';
import type { MarkedStyles } from 'react-native-marked/src/theme/types';
import { Image, ScrollView } from 'tamagui';
import type { CacheResultObject } from './hooks/PhotoQuestionAnswerResult.type';
import { usePhotoQuestionAnswerResultItem } from './hooks/usePhotoQuestionAnswerResultItem';

type Props = {
  activeIndex?: number;
  index: number;
  handleCacheResultObject: (item: CacheResultObject) => void;
  getCacheResultObject: (key: string) => CacheResultObject | undefined;
  getCurrentRectImgData: (
    rect: [number, number, number, number],
  ) => Promise<{ data: string; width: number; height: number } | undefined>;
  boxRect: PhotoCutRectDTO;
};
export const PhotoQuestionAnswerResultItemContent = (props: Props) => {
  const {
    activeIndex,
    index,
    getCurrentRectImgData,
    boxRect,
    handleCacheResultObject,
    getCacheResultObject,
  } = props;

  const { imgData, fail, aiLoading, content, retry } =
    usePhotoQuestionAnswerResultItem({
      activeIndex,
      index,
      getCurrentRectImgData,
      boxRect,
      handleCacheResultObject,
      getCacheResultObject,
    });

  const [, y1, , y2] = boxRect.rect;

  const handleImagePress = useCallback(
    (pressMode: string, url: string) => {},
    [],
  );

  const customMarkdownRenderer = useMemo(
    () => new CustomMarkdownRenderer(handleImagePress),
    [handleImagePress],
  );

  const customTokenizer = useMemo(() => new CustomTokenizer(), []);

  const answerContent = useMemo(() => {
    if (fail) {
      return (
        <JglTouchable
          jglClassName='flex-center'
          minH={140}
          style={{
            flexDirection: 'column',
          }}
          onPress={retry}
        >
          <Image
            source={{ uri: AcIcon.AnswerLoadFail }}
            width={74}
            height={74}
          />
          <JglText jglClassName='mt-[4px] text-[#707070]' fontSize={16}>
            答案加载失败
          </JglText>
        </JglTouchable>
      );
    } else {
      if (aiLoading) {
        return (
          <JglXStack jglClassName='flex-center' minH={140}>
            <ActivityIndicator size={30} />
            <JglText fontSize={16} ml={4} color='#707070'>
              答案加载中
            </JglText>
          </JglXStack>
        );
      } else {
        const renderArr = splitContentByTitle(content ?? '');
        return (
          <>
            <JglYStack space={16}>
              {renderArr.map((item, arrIndex) => {
                const { title, content: renderContent } = item;
                return (
                  <JglYStack key={title}>
                    <JglXStack mb={10}>
                      <JglYStack pos='relative'>
                        <JglText
                          fontSize={16}
                          color='#1f1f1f'
                          fontWeight='bold'
                          zIndex={20}
                        >
                          {title}
                        </JglText>
                        {!isEmpty(renderContent) ? (
                          <JglView
                            bottom={2}
                            h={6}
                            bg='#7191FFE0'
                            borderRadius={16}
                            pos='absolute'
                            w='100%'
                          />
                        ) : undefined}
                      </JglYStack>
                    </JglXStack>
                    <JglYStack
                      p={12}
                      borderRadius={8}
                      borderColor='#F0F0F0'
                      borderWidth={1}
                      style={{
                        display: isEmpty(renderContent) ? 'none' : 'flex',
                      }}
                    >
                      <Markdown
                        value={renderContent}
                        styles={customMarkedStyles}
                        renderer={customMarkdownRenderer}
                        tokenizer={customTokenizer}
                      />
                    </JglYStack>
                  </JglYStack>
                );
              })}
            </JglYStack>

            <AiEndTip />
          </>
        );
      }
    }
  }, [
    aiLoading,
    content,
    customMarkdownRenderer,
    customTokenizer,
    fail,
    retry,
  ]);

  return (
    <JglYStack flex={1}>
      <ScrollView
        style={{ flex: 1, minHeight: '100%' }}
        contentContainerStyle={{ paddingTop: 12 }}
      >
        <JglYStack px={16}>
          <JglXStack>
            <JglYStack pos='relative'>
              <JglText
                fontSize={18}
                fontWeight='bold'
                style={{ color: '#232323' }}
              >
                识别题目
              </JglText>
              <JglView
                bottom={2}
                h={6}
                bg='#7191FFE0'
                borderRadius={16}
                pos='absolute'
                w='100%'
              />
            </JglYStack>
          </JglXStack>
          <JglYStack
            my={16}
            borderRadius={8}
            borderColor='#F0F0F0'
            borderWidth={1}
            justifyContent='center'
            minH={Math.abs(y2 - y1) + 24}
            p={12}
          >
            {imgData ? (
              <Image
                source={{ uri: imgData.data }}
                width={'100%'}
                aspectRatio={imgData.width / imgData.height}
                resizeMode='cover'
              />
            ) : (
              <ActivityIndicator />
            )}
          </JglYStack>
        </JglYStack>
        <LinearGradient
          className='relative'
          colors={['#E4F3FF', '#FEFDFF']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={{
            paddingTop: 17,
            paddingBottom: 80,
            borderTopLeftRadius: 16,
            borderTopRightRadius: 16,
          }}
        >
          <LinearGradient
            style={StyleSheet.absoluteFill}
            colors={['rgba(255,255,255,0)', 'rgba(255,255,255,1)']}
          />
          <JglXStack justifyContent='space-between' px={16}>
            <Image source={{ uri: Icon.aiAnswerTag }} width={88} height={24} />
          </JglXStack>
        </LinearGradient>
        <JglYStack mt={-60} px={16}>
          {answerContent}
        </JglYStack>
      </ScrollView>
    </JglYStack>
  );
};

const customMarkedStyles: MarkedStyles = {
  table: { marginVertical: 4 },
  li: { paddingVertical: 4 },
  h1: { fontSize: 28 },
  h2: { fontSize: 24 },
  h3: { fontSize: 20 },
  h4: { fontSize: 18 },
  blockquote: { marginVertical: 8 },
  paragraph: { paddingVertical: 6 },
};
