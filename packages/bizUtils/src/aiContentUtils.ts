/** 拆分拍照答疑ai返回数据 */
export const splitContentByTitle = (str: string) => {
  // 使用正则表达式匹配【】中的标题和后续内容
  // const regex = /【(.*?)】([\s\S]*?)(?=\n【|$)/g;
  // const result: { title: string; content: string }[] = [];
  // let match;

  // // 遍历所有匹配的内容
  // while ((match = regex.exec(str)) !== null) {
  //   const title = match[1].trim(); // 获取标题
  //   const content = match[2].trim(); // 获取内容
  //   result.push({ title, content }); // 将标题和内容存入数组
  // }

  // return result.length ? result : [{ title: str, content: '' }];

  return [{ title: '解析', content: str }];
};
