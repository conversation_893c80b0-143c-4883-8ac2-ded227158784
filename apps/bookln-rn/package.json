{"name": "@bookln-app/bookln-rn", "main": "./index.ts", "version": "1.0.0", "private": true, "nx": {"tags": ["scope:apps"]}, "scripts": {"start": "expo start", "start:clear": "expo start --clear", "android": "expo run:android", "ios": "expo run:ios", "ios:init": "echo 如果提示baseInfo.json找不到就可以执行一下 && (cd ios && bundle exec fastlane switch_app app_id:2 is_inhouse:false is_dev:false)", "pod": "(cd ios && bundle install && bundle exec pod install)", "pod:c": "(cd ios && bundle install && bundle exec pod deintegrate && bundle exec pod repo update --verbose && bundle exec pod install)", "web": "expo start --web", "xcode": "open ios/bookln.xcworkspace", "as": "open -a /Applications/Android' 'Studio.app android", "test": "jest --watchAll", "build:ios": "npx tsx scripts/build-ios.mts", "build:ios:dev": "npx tsx scripts/build-ios-dev.mts", "build:ios:upload": "npx tsx scripts/build-ios-upload.mts", "build:android": "npx tsx scripts/build-android.mts", "build:android:dev": "npx tsx scripts/build-android-dev.mts", "build:android:release": "npx tsx scripts/build-android.mts --isAndroidChannelRelease=true", "bundlejs:ios": "npx react-native bundle --entry-file='node_modules/expo-router/entry.js' --bundle-output='./ios/main.jsbundle' --dev=false --platform='ios' --assets-dest='./ios'", "bundlejs:android": "npx react-native bundle --entry-file='node_modules/expo-router/entry.js' --bundle-output='./android/main.jsbundle' --dev=false --platform='android' --assets-dest='./android'"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@bookln/bookln-biz": "workspace:*", "@bookln/permission": "workspace:*", "@config-plugins/react-native-blob-util": "6.0.0", "@config-plugins/react-native-pdf": "6.0.0", "@expo/react-native-action-sheet": "4.0.1", "@jgl/biz-components": "workspace:*", "@lodev09/react-native-exify": "0.2.7", "@react-native-async-storage/async-storage": "1.18.2", "@react-native-camera-roll/camera-roll": "7.0.0", "@react-native-clipboard/clipboard": "1.12.1", "@react-native-community/blur": "4.3.2", "@react-native-community/hooks": "3.0.0", "@react-native-community/netinfo": "11.4.1", "@react-native-voice/voice": "3.2.4", "@react-navigation/drawer": "6.6.14", "@react-navigation/native": "6.1.6", "@sentry/react-native": "6.15.1", "@shopify/flash-list": "1.6.3", "@shopify/react-native-skia": "0.1.234", "@sleiv/react-native-graceful-exit": "0.1.0", "@yunti-private/logger-rn": "3.0.3", "@yunti-private/rn-expo-updates-helper": "0.1.13", "@yunti-private/rn-iflytek": "0.1.6", "@yunti-private/rn-suntone": "0.0.8", "@yunti-private/rn-umeng": "1.0.16", "@yunti/react-native-chivox": "https://github.com/yuntitech/react-native-chivox#405cbd3857de65d5bcbfd53d238734e3237c5277", "expo": "49.0.21", "expo-av": "13.4.1", "expo-barcode-scanner": "12.7.0", "expo-camera": "13.6.0", "expo-constants": "14.4.2", "expo-dev-client": "2.4.12", "expo-font": "11.4.0", "expo-image": "1.8.1", "expo-image-manipulator": "11.6.0", "expo-image-picker": "14.5.0", "expo-linear-gradient": "12.5.0", "expo-linking": "5.0.2", "expo-localization": "14.3.0", "expo-router": "2.0.9", "expo-screen-orientation": "6.2.0", "expo-splash-screen": "0.20.5", "expo-status-bar": "1.6.0", "expo-system-ui": "2.4.0", "expo-updates": "0.18.19", "expo-web-browser": "12.3.2", "lottie-react-native": "6.4.0", "lucide-react-native": "0.511.0", "mitt": "3.0.1", "native-wechat": "1.0.21", "react-native": "0.72.6", "react-native-audio-recorder-player": "3.6.12", "react-native-blob-util": "0.19.4", "react-native-collapsible-tab-view": "8.0.1", "react-native-context-menu-view": "1.19.0", "react-native-device-info": "10.12.1", "react-native-drawer-layout": "3.3.0", "react-native-drop-shadow": "1.0.0", "react-native-fs": "2.20.0", "react-native-gesture-handler": "2.13.4", "react-native-haptic-feedback": "2.2.0", "react-native-iap": "12.16.2", "react-native-image-colors": "2.3.0", "react-native-image-viewing": "0.2.2", "react-native-linear-gradient": "2.8.3", "react-native-markdown-display": "7.0.2", "react-native-marked": "6.0.7", "react-native-math-view": "3.9.5", "react-native-modal": "13.0.1", "react-native-modalbox": "2.0.2", "react-native-pager-view": "6.0.2", "react-native-pdf": "6.7.1", "react-native-permissions": "3.10.1", "react-native-progress": "5.0.1", "react-native-qrcode-svg": "5.1.2", "react-native-reanimated": "3.6.1", "react-native-reanimated-carousel": "4.0.2", "react-native-reanimated-table": "0.0.2", "react-native-root-siblings": "5.0.1", "react-native-root-toast": "3.5.1", "react-native-safe-area-context": "4.6.3", "react-native-screens": "software-mansion/react-native-screens#42d9c36a73b24136aebb44dba73a05adbb2e03dc", "react-native-share": "10.0.2", "react-native-skia-shadow": "1.1.0", "react-native-snap-carousel": "3.9.1", "react-native-sound": "https://github.com/yuntitech/react-native-sound#11581dc4794ba43957ce90da2df9814ed24d8578", "react-native-svg": "13.14.0", "react-native-swiper": "1.6.0", "react-native-toast-hybrid": "2.5.0", "react-native-user-agent": "https://github.com/yuntitech/react-native-user-agent.git", "react-native-view-shot": "3.8.0", "react-native-webview": "13.6.3", "react-syntax-highlighter": "15.6.1", "rn-alioss": "0.2.5", "rn-sprite-sheet": "1.1.12"}, "devDependencies": {"@types/marked": "5.0.0", "@types/react-native-table-component": "1.2.8", "@types/react-syntax-highlighter": "15.5.13"}}